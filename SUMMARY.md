# Summary

+ [@tidepool/viz developer guide](docs/StartHere.md)
    + [background](docs/Background.md)
    + [overview of features](docs/FeatureOverview.md)
    + [planned architecture](docs/Architecture.md)
    + [app & directory structure](docs/DirectoryStructure.md)
    + [code style](docs/CodeStyle.md)
+ [per-view documentation](docs/views/README.md)
    + [Device Settings view](src/components/settings/README.md)
    + [Trends view](docs/views/Trends.md)
+ [use of React Storybook](docs/Storybook.md)
+ [usage of dependencies](docs/deps/README.md)
    + [D3](docs/deps/D3.md)
    + [GSAP](docs/deps/GSAP.md)
    + [Moment](docs/deps/Moment.md)
    + [React](docs/deps/React.md)
    + [React Motion](docs/deps/ReactMotion.md)
    + [Redux](docs/deps/Redux.md)
    + [webpack](docs/deps/Webpack.md)
+ [utilities](src/utils/README.md)
    + [API docs for utilities](src/utils/apidocs/README.md)
        + [basal](src/utils/apidocs/basal.md)
        + [blood glucose](src/utils/apidocs/bloodglucose.md)
        + [bolus](src/utils/apidocs/bolus.md)
        + [datetime](src/utils/apidocs/datetime.md)
        + [format](src/utils/apidocs/format.md)
        + [misc](src/utils/apidocs/misc.md)
+ [misc](docs/misc/README.md)
    + [Common props](docs/misc/CommonProps.md)
    + [Docs setup & publishing](docs/misc/Docs.md)
    + [Time-rendering modes](docs/misc/TimeRenderingModes.md)
