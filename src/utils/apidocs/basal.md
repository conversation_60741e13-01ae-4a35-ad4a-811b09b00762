
> @tidepool/viz@0.8.1 apidocs /Users/<USER>/Tidepool/viz
> jsdoc2md "src/utils/basal.js"

## Functions

<dl>
<dt><a href="#getBasalSequences">getBasalSequences(basals)</a> ⇒ <code>Array</code></dt>
<dd><p>getBasalSequences</p>
</dd>
</dl>

<a name="getBasalSequences"></a>

## getBasalSequences(basals) ⇒ <code>Array</code>
getBasalSequences

**Kind**: global function
**Returns**: <code>Array</code> - Array of Arrays where each component Array is a sequence of basals
                of the same subType to be rendered as a unit

| Param | Type | Description |
| --- | --- | --- |
| basals | <code>Array</code> | Array of preprocessed Tidepool basal objects |
