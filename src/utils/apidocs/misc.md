
> @tidepool/viz@0.8.1 apidocs /Users/<USER>/Tidepool/viz
> jsdoc2md "src/utils/misc.js"

<a name="getPatientFullName"></a>

## getPatientFullName(patient) ⇒ <code>String</code>
getPatientFullName

**Kind**: global function  
**Returns**: <code>String</code> - Pw<PERSON>'s full name (first & last)  

| Param | Type | Description |
| --- | --- | --- |
| patient | <code>Object</code> | Tidepool patient object containing profile |

