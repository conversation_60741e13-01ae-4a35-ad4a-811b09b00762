/*
 * == BSD2 LICENSE ==
 * Copyright (c) 2016, Tidepool Project
 *
 * This program is free software; you can redistribute it and/or modify it under
 * the terms of the associated License, which is identical to the BSD 2-Clause
 * License as published by the Open Source Initiative at opensource.org.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the License for more details.
 *
 * You should have received a copy of the License along with this program; if
 * not, you can obtain one from Tidepool Project at tidepool.org.
 * == BSD2 LICENSE ==
 */

.whiteText {
  color: white;
  fill: white;
}

.boldText {
  font-weight: bold;
}

.lightText {
  font-weight: 300;
}

.smallSize {
  font-size: 12px;
}

.axisSize {
  font-size: 14px;
}

.defaultSize {
  font-size: 14px;
}

.largeSize {
  font-size: 16px;
}

.extraLargeSize {
  font-size: 24px;
}

.mediumContrastText {
  color: var(--text-medium-contrast);
  fill: var(--text-medium-contrast);
}

.highContrastText {
  color: var(--text-high-contrast);
  fill: var(--text-high-contrast);
}

.noWrap {
  white-space: nowrap;
}

.svgMiddleAnchored {
  text-anchor: middle;
}

.svgRightAnchored {
  text-anchor: end;
}

.svgStartAnchored {
  text-anchor: start;
}

.svgVerticalCentered {
  dominant-baseline: central;
}
