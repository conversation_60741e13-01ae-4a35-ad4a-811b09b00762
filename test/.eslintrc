{"env": {"mocha": true, "node": true}, "globals": {"assert": false, "expect": false, "sinon": false}, "rules": {"comma-dangle": ["error", "only-multiline"], "no-multiple-empty-lines": 0, "no-mixed-operators": 0, "no-redeclare": 0, "function-call-argument-newline": 0, "max-len": 0, "max-classes-per-file": 0, "no-unused-vars": 0, "no-import-assign": 0, "function-paren-newline": 0, "import/first": 0, "import/no-useless-path-segments": 0, "indent": 0, "lodash/prefer-lodash-method": ["error", {"ignoreMethods": ["find", "split", "trim", "replace"]}], "no-prototype-builtins": 0, "no-unused-expressions": 0, "no-useless-escape": 0, "object-property-newline": 0, "operator-assignment": 0, "react/jsx-curly-brace-presence": 0, "react/jsx-props-no-spreading": 0, "react/function-component-definition": 0, "space-unary-ops": 0}}